<?php
// Set page title
$page_title = 'Dashboard';

// Include header
require_once 'includes/header.php';

// Include pledge system functions
require_once 'includes/pledge_system.php';

// Include blog functions
require_once 'includes/blog_functions.php';

// Include leaderboard functions
require_once 'includes/leaderboard_functions.php';

// Get user token balance and bonus tokens
$token_balance = get_token_balance($user_id, $db);
$bonus_tokens = get_bonus_tokens($user_id, $db);

// Get user's referral code and queue status (amount-aware)
$query = "SELECT referral_code, pledges_to_receive, amount_to_receive, original_pledge_amount FROM users WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user_data = $stmt->fetch(PDO::FETCH_OBJ);
$referral_code = $user_data->referral_code;
$pledges_to_receive = $user_data->pledges_to_receive ?? 0;
$amount_to_receive = $user_data->amount_to_receive ?? 0;
$original_pledge_amount = $user_data->original_pledge_amount ?? 0;

// Get user pledge stats
$pledge_stats = get_user_pledge_stats($db, $user_id);

// Get active pledges count
$query = "SELECT COUNT(*) as count FROM pledges WHERE user_id = :user_id AND status IN ('pending', 'matched')";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_OBJ);
$active_pledges = $result->count;

// Get active matches count
$query = "SELECT COUNT(*) as count FROM matches WHERE (sender_id = :user_id OR receiver_id = :user_id) AND status IN ('pending', 'payment_sent')";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_OBJ);
$active_matches = $result->count;

// Get user's referral rank and summary for leaderboard preview
$user_rank = get_user_referral_rank($db, $user_id);
$user_referral_summary = get_user_referral_summary($db, $user_id);
$top_3_referrers = get_top_referrers($db, 3);

// Get recent notifications
$query = "SELECT * FROM notifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$recent_notifications = $stmt->fetchAll(PDO::FETCH_OBJ);

// Include announcement functions for styling
require_once 'includes/announcement_functions.php';
require_once 'includes/scheduling_functions.php';

// Update announcement statuses first
update_announcement_statuses($db);

// Get latest active announcement for this user using scheduling-aware function
$latest_announcement = get_active_announcements($db, $user_id);

// Fetch blog posts for the dashboard widget
$dashboard_blog_posts = fetch_blog_posts(3); // Get 3 latest blog posts for dashboard
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Dashboard</h1>
    </div>

    <?php if ($latest_announcement): ?>
    <?php
    // Debug: Show announcement data (remove this in production)
    if (isset($_GET['debug'])) {
        echo '<div class="alert alert-info"><pre>' . print_r($latest_announcement, true) . '</pre></div>';
    }

    // Get priority styling
    $priority = $latest_announcement->priority ?? 'normal';
    $priority_styling = get_priority_styling($priority);
    $is_pinned = $latest_announcement->is_pinned ?? false;
    ?>
    <!-- Enhanced System Announcement -->
    <div class="enhanced-announcement-container mb-4 <?php echo $is_pinned ? 'pinned-announcement' : ''; ?>" id="announcement-container">
        <div class="announcement-card <?php echo $priority_styling['alert_class']; ?> alert-dismissible fade show enhanced-announcement"
             role="alert"
             data-priority="<?php echo $priority; ?>"
             style="border-left: 5px solid <?php echo $priority_styling['color']; ?>;">

            <!-- Header Section: Title, Priority Badges, and Pin Status -->
            <div class="announcement-header-section">
                <div class="announcement-header d-flex align-items-center">
                    <div class="announcement-icon mr-3">
                        <i class="<?php echo $priority_styling['icon']; ?> fa-2x" style="color: <?php echo $priority_styling['color']; ?>;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="announcement-title-section">
                                <h4 class="announcement-title mb-1"><?php echo htmlspecialchars($latest_announcement->title); ?></h4>
                                <div class="announcement-meta">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> Posted: <?php echo format_date($latest_announcement->created_at); ?>
                                    </small>
                                </div>
                            </div>
                            <div class="announcement-badges">
                                <?php if ($is_pinned): ?>
                                    <span class="badge badge-warning mr-2">
                                        <i class="fas fa-thumbtack"></i> Pinned
                                    </span>
                                <?php endif; ?>
                                <span class="badge <?php echo $priority_styling['badge_class']; ?>">
                                    <?php echo ucfirst($priority); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Section: Announcement Message/Description -->
            <div class="announcement-content-section">
                <div class="announcement-message">
                    <?php echo nl2br(htmlspecialchars($latest_announcement->message)); ?>
                </div>

                <!-- Link Previews (if any) -->
                <?php if (!empty($latest_announcement->link_preview_data)): ?>
                <div class="announcement-link-previews mt-3">
                    <?php
                    $link_previews = json_decode($latest_announcement->link_preview_data, true);
                    if ($link_previews && is_array($link_previews)) {
                        foreach ($link_previews as $preview) {
                            echo generate_link_preview_html($preview);
                        }
                    }
                    ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Media Section: Videos and Images at the Bottom -->
            <?php
            $has_media = !empty($latest_announcement->video_url) ||
                        !empty($latest_announcement->image_file) ||
                        !empty($latest_announcement->video_file);
            ?>
            <?php if ($has_media): ?>
            <div class="announcement-media-section">
                <div class="media-separator"></div>

                <!-- Video URL Embed -->
                <?php if (!empty($latest_announcement->video_url)): ?>
                <div class="media-item video-embed-item">
                    <div class="media-label">
                        <i class="fab fa-youtube"></i> Featured Video
                    </div>
                    <div class="media-content">
                        <?php
                        $video_embed = generate_video_embed($latest_announcement->video_url, $latest_announcement->title);
                        if ($video_embed) {
                            echo $video_embed;
                        } else {
                            echo '<div class="alert alert-warning mb-0">Unable to embed video: ' . htmlspecialchars($latest_announcement->video_url) . '</div>';
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Image Display -->
                <?php if (!empty($latest_announcement->image_file)): ?>
                <div class="media-item image-item">
                    <div class="media-label">
                        <i class="fas fa-image"></i> Attachment
                    </div>
                    <div class="media-content">
                        <?php $image_path = "uploads/announcements/{$latest_announcement->image_file}"; ?>
                        <div class="announcement-image-container">
                            <img src="<?php echo $image_path; ?>"
                                 class="announcement-image img-fluid rounded shadow-sm"
                                 alt="Announcement image"
                                 onclick="openImageModal(this.src)">
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Video File Display -->
                <?php if (!empty($latest_announcement->video_file)): ?>
                <div class="media-item video-file-item">
                    <div class="media-label">
                        <i class="fas fa-video"></i> Video File
                    </div>
                    <div class="media-content">
                        <?php $video_path = "uploads/announcements/{$latest_announcement->video_file}"; ?>
                        <div class="announcement-video-container">
                            <video controls class="announcement-video w-100 rounded shadow-sm">
                                <source src="<?php echo $video_path; ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Footer Section -->
            <div class="announcement-footer-section">
                <?php if ($is_pinned): ?>
                    <div class="pinned-notice">
                        <small class="text-muted">
                            <i class="fas fa-thumbtack"></i> This announcement is pinned and will remain visible
                        </small>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Close Button -->
            <button type="button" class="close announcement-close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>
    <?php endif; ?>

    <!-- Welcome Header -->
    <div class="jumbotron bg-light">
        <h2>Welcome, <?php echo $_SESSION['user_name']; ?>!</h2>
        <p class="lead">Your current token balance: <strong><?php echo format_currency($token_balance, 'Tokens'); ?></strong></p>
        <p>Status: <span class="badge badge-success">Active</span></p>
    </div>

    <!-- Quick Access Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card quick-access-card">
                <div class="card-body">
                    <i class="fas fa-hand-holding-usd"></i>
                    <h5 class="card-title">Make a Pledge</h5>
                    <p class="card-text">Pledge your tokens to help others in the community.</p>
                    <a href="pledges.php" class="btn btn-primary">Go to Pledges</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card quick-access-card">
                <div class="card-body">
                    <i class="fas fa-coins"></i>
                    <h5 class="card-title">Buy Tokens</h5>
                    <p class="card-text">Purchase tokens to participate in the platform.</p>
                    <a href="wallet.php" class="btn btn-primary">Go to Wallet</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card quick-access-card">
                <div class="card-body">
                    <i class="fas fa-exchange-alt"></i>
                    <h5 class="card-title">Active Matches</h5>
                    <p class="card-text">View and manage your current matches.</p>
                    <a href="matches.php" class="btn btn-primary">Go to Matches</a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card quick-access-card">
                <div class="card-body">
                    <i class="fas fa-headset"></i>
                    <h5 class="card-title">Support Tickets</h5>
                    <p class="card-text">Need help? Create a support ticket for assistance.</p>
                    <a href="support_tickets.php" class="btn btn-primary">Get Support</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Section -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Your Activity Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h6>Active Pledges</h6>
                                <h2><?php echo $active_pledges; ?></h2>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center mb-3">
                                <h6>Active Matches</h6>
                                <h2><?php echo $active_matches; ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="pledges.php" class="btn btn-outline-primary mr-2">View Pledges</a>
                        <a href="matches.php" class="btn btn-outline-primary">View Matches</a>
                    </div>
                </div>
            </div>

            <!-- Pledge System Status -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Pledge System Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>How It Works</h6>
                            <p>Make a pledge (GHS 20, 50, 100, or 200) to help someone, and you'll be placed in queue to receive <strong>exactly double your pledge amount</strong> in return!</p>
                            <div class="d-flex justify-content-between">
                                <div class="text-center">
                                    <h6>Pledges Made</h6>
                                    <h3><?php echo $pledge_stats->pledges_made; ?></h3>
                                </div>
                                <div class="text-center">
                                    <h6>Pledges Received</h6>
                                    <h3><?php echo $pledge_stats->pledges_received; ?></h3>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="pledges.php" class="btn btn-primary">
                                    <i class="fas fa-hand-holding-usd"></i> Make a Pledge
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h6>Your Queue Status</h6>
                                <?php if ($pledges_to_receive > 0): ?>
                                    <div class="alert alert-success">
                                        <h3>You are in the queue!</h3>
                                        <?php if ($amount_to_receive > 0): ?>
                                            <p class="mb-1">You still need to receive <strong>GHS <?php echo number_format($amount_to_receive, 2); ?></strong></p>
                                            <p class="mb-0">
                                                <small class="text-muted">
                                                    Original pledge: GHS <?php echo number_format($original_pledge_amount, 2); ?> × 2 = GHS <?php echo number_format($original_pledge_amount * 2, 2); ?>
                                                </small>
                                            </p>
                                        <?php else: ?>
                                            <p class="mb-0">You will receive <strong><?php echo $pledges_to_receive; ?></strong> more pledge(s) totaling double your pledge amount.</p>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <h3>Not in queue</h3>
                                        <p class="mb-0">Make a pledge to join the queue and receive double your pledge amount!</p>
                                    </div>
                                <?php endif; ?>

                                <?php if ($active_pledges > 0): ?>
                                    <div class="alert alert-warning mt-2">
                                        <p class="mb-0">You have <?php echo $active_pledges; ?> active pledge(s) in progress.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Program -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Referral Program</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Earn Bonus Tokens!</h6>
                            <p>Invite friends and earn 10 bonus tokens for each friend who joins and makes their first token purchase.</p>
                            <p><strong>Your Referral Code:</strong> <?php echo $referral_code; ?></p>
                            <a href="referrals.php" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Invite Friends
                            </a>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h6>Your Bonus Tokens</h6>
                                <h2><?php echo format_currency($bonus_tokens, 'Tokens'); ?></h2>
                                <?php if ($bonus_tokens >= 100): ?>
                                    <div class="alert alert-success mt-2">
                                        <p class="mb-0">You can now redeem your bonus tokens!</p>
                                    </div>
                                    <a href="referrals.php" class="btn btn-outline-success">Redeem Now</a>
                                <?php else: ?>
                                    <div class="progress mt-2">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo min(100, ($bonus_tokens / 100) * 100); ?>%" aria-valuenow="<?php echo $bonus_tokens; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <small class="text-muted"><?php echo $bonus_tokens; ?>/100 tokens needed to redeem</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Notifications</h5>
                    <a href="notifications.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php if (empty($recent_notifications)): ?>
                            <li class="list-group-item text-center">No recent notifications</li>
                        <?php else: ?>
                            <?php foreach ($recent_notifications as $notification): ?>
                                <li class="list-group-item <?php echo $notification->read_status ? '' : 'bg-light'; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo $notification->title; ?></h6>
                                            <p class="mb-1 text-muted"><?php echo $notification->message; ?></p>
                                        </div>
                                        <small class="text-muted"><?php echo format_date($notification->created_at, 'd M'); ?></small>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Blog Widget -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-blog"></i>
                        Latest from Our Blog
                    </h5>
                    <a href="https://blog.p2pdonate.com" class="btn btn-sm btn-outline-light" target="_blank" rel="noopener noreferrer">
                        <i class="fas fa-external-link-alt"></i>
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php if (empty($dashboard_blog_posts)): ?>
                            <li class="list-group-item text-center">
                                <i class="fas fa-blog fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No blog posts available</p>
                            </li>
                        <?php else: ?>
                            <?php foreach ($dashboard_blog_posts as $post): ?>
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <a href="<?php echo htmlspecialchars($post['url']); ?>"
                                                   target="_blank"
                                                   rel="noopener noreferrer"
                                                   class="text-decoration-none text-dark">
                                                    <?php echo htmlspecialchars($post['title']); ?>
                                                </a>
                                            </h6>
                                            <p class="mb-1 text-muted small">
                                                <?php echo htmlspecialchars(truncate_text($post['excerpt'], 80)); ?>
                                            </p>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt"></i>
                                                <?php echo htmlspecialchars($post['date']); ?>
                                            </small>
                                        </div>
                                        <a href="<?php echo htmlspecialchars($post['url']); ?>"
                                           target="_blank"
                                           rel="noopener noreferrer"
                                           class="btn btn-sm btn-outline-success ml-2">
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Preview Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning leaderboard-preview">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy"></i> Referral Leaderboard
                        </h5>
                        <a href="leaderboard.php" class="btn btn-sm btn-dark">
                            <i class="fas fa-eye"></i> View Full Leaderboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-muted">Your Current Rank</h6>
                                    <h3 class="text-success">
                                        <i class="rank-icon <?php echo get_rank_icon($user_rank); ?>"></i>
                                        <?php echo format_rank($user_rank); ?>
                                    </h3>
                                    <small class="text-muted">
                                        <?php echo $user_referral_summary->completed_referrals ?? 0; ?> successful referrals
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Top 3 Referrers</h6>
                            <?php if (empty($top_3_referrers)): ?>
                                <div class="alert alert-info">
                                    <small>No referrers yet! Be the first to claim the top spot.</small>
                                </div>
                            <?php else: ?>
                                <?php foreach ($top_3_referrers as $index => $referrer): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <span class="rank-badge <?php echo get_rank_badge_class($index + 1); ?>">
                                                <i class="rank-icon <?php echo get_rank_icon($index + 1); ?>"></i>
                                                <?php echo $index + 1; ?>
                                            </span>
                                            <strong class="ml-2"><?php echo htmlspecialchars($referrer->name); ?></strong>
                                            <?php if ($referrer->id == $user_id): ?>
                                                <span class="badge badge-info ml-1">You</span>
                                            <?php endif; ?>
                                        </div>
                                        <span class="badge badge-success">
                                            <?php echo $referrer->completed_referrals; ?> referrals
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ($user_rank == 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle"></i>
                            <strong>Want to join the leaderboard?</strong>
                            Start referring friends to earn your place among the top referrers!
                            <a href="referrals.php" class="alert-link">Get your referral code here</a>.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
